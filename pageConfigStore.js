const fs = require('fs').promises;
const path = require('path');
const logger = require('./logger');

// 定义持久化存储路径（数据目录 + JSON文件）
let DATA_DIR;
// 检查是否为 pkg 打包环境（process.pkg 是 pkg 注入的变量）
if (process.pkg) {
  // 打包后：使用当前工作目录（运行可执行文件时的目录）下的 data 文件夹
  DATA_DIR = path.join(process.cwd(), './data');
} else {
  // 开发环境：仍使用代码目录下的 data 文件夹
  DATA_DIR = path.join(__dirname, './data');
}

const CONFIG_FILE = path.join(DATA_DIR, 'pageConfigs.json');

class PageConfigStore {
  constructor() {
    // 内存缓存：{ pageKey: { urlPattern, urlRegex, elements } }
    this.pageConfigs = new Map();
    // 初始化：确保数据目录存在 + 加载已持久化的配置
    this.init();
  }

  /**
   * 初始化：创建数据目录 + 加载历史配置
   */
  async init() {
    try {
      // 1. 检查并创建data目录（不存在则创建）
      await fs.access(DATA_DIR).catch(async () => {
        await fs.mkdir(DATA_DIR, { recursive: true });
        console.log(`[持久化] 创建数据目录: ${DATA_DIR}`);
        logger.info(`[持久化] 创建数据目录: ${DATA_DIR}`);
      });

      // 2. 加载JSON文件中的历史配置
      await this.loadFromFile();
      console.log(`[持久化] 加载页面配置成功，共${this.pageConfigs.size}个页面`);
      logger.info(`[持久化] 加载页面配置成功，共${this.pageConfigs.size}个页面`);
    } catch (error) {
      console.error(`[持久化] 初始化失败: ${error.message}`);
      logger.error(`[持久化] 初始化失败: ${error.message}`);
      // 初始化失败时，内存配置置空（避免后续操作异常）
      this.pageConfigs = new Map();
    }
  }

  /**
   * 从JSON文件加载配置到内存
   */
  async loadFromFile() {
    try {
      // 检查文件是否存在（不存在则视为空配置）
      await fs.access(CONFIG_FILE);
      
      // 读取文件内容并解析JSON
      const fileContent = await fs.readFile(CONFIG_FILE, 'utf8');
      const savedConfigs = JSON.parse(fileContent);

      // 将JSON对象转为Map（处理URL正则表达式）
      this.pageConfigs = new Map();
      for (const [pageKey, config] of Object.entries(savedConfigs)) {
        // 恢复URL正则（JSON存储的是字符串，需重新编译）
        const urlRegex = config.urlPattern ? new RegExp(config.urlPattern) : null;
        this.pageConfigs.set(pageKey, {
          ...config,
          urlRegex // 覆盖JSON中的字符串，转为RegExp对象
        });
      }
    } catch (error) {
      // 文件不存在或解析错误，视为初始空配置
      if (error.code === 'ENOENT') {
        console.log(`[持久化] 未找到配置文件，将创建新文件: ${CONFIG_FILE}`);
        logger.info(`[持久化] 未找到配置文件，将创建新文件: ${CONFIG_FILE}`);
      } else {
        console.error(`[持久化] 加载配置文件失败: ${error.message}`);
        logger.error(`[持久化] 加载配置文件失败: ${error.message}`);
      }
      this.pageConfigs = new Map();
    }
  }

  /**
   * 将内存中的配置持久化到JSON文件
   */
  async saveToFile() {
    try {
      // 将Map转为JSON可序列化的对象（处理RegExp：存储为字符串）
      const configToSave = {};
      for (const [pageKey, config] of this.pageConfigs) {
        configToSave[pageKey] = {
          ...config,
          urlRegex: undefined, // 移除RegExp对象（无法序列化）
          urlPattern: config.urlPattern || null // 存储原始正则字符串
        };
      }

      // 写入文件（格式化JSON，便于人工编辑）
      await fs.writeFile(
        CONFIG_FILE,
        JSON.stringify(configToSave, null, 2),
        'utf8'
      );
      console.log(`[持久化] 配置已保存到文件: ${CONFIG_FILE}`);
      logger.info(`[持久化] 配置已保存到文件: ${CONFIG_FILE}`);
    } catch (error) {
      console.error(`[持久化] 保存配置文件失败: ${error.message}`);
      logger.error(`[持久化] 保存配置文件失败: ${error.message}`);
      throw new Error(`配置持久化失败: ${error.message}`);
    }
  }

  /**
   * 添加/更新页面配置（自动持久化）
   * @param {string} pageKey 页面唯一标识
   * @param {string|RegExp} urlPattern URL匹配正则（可选）
   * @param {Object} elements 元素配置（{ 元素名: { selector, type } }）
   */
  async addPageConfig(pageKey, urlPattern, elements) {
    if (!pageKey || !elements) {
      throw new Error('pageKey和elements为必填项');
    }

    // 处理URL模式（统一转为字符串，便于持久化）
    const urlPatternStr = urlPattern instanceof RegExp 
      ? urlPattern.source 
      : (urlPattern || null);
    const urlRegex = urlPatternStr ? new RegExp(urlPatternStr) : null;

    // 获取现有配置（如果存在）以保留创建时间
    const existingConfig = this.pageConfigs.get(pageKey);
    const now = new Date().toISOString();

    // 更新内存配置
    this.pageConfigs.set(pageKey, {
      urlPattern: urlPatternStr,
      urlRegex,
      elements,
      createdAt: existingConfig?.createdAt || now,
      updatedAt: now
    });

    // 自动持久化到文件
    await this.saveToFile();
    console.log(`[页面配置] 新增/更新页面: ${pageKey}`);
    logger.info(`[页面配置] 新增/更新页面: ${pageKey}`);
  }

  /**
   * 删除页面配置（自动持久化）
   * @param {string} pageKey 页面唯一标识
   */
  async removePageConfig(pageKey) {
    if (this.pageConfigs.has(pageKey)) {
      this.pageConfigs.delete(pageKey);
      // 自动持久化到文件
      await this.saveToFile();
      console.log(`[页面配置] 删除页面: ${pageKey}`);
      logger.info(`[页面配置] 删除页面: ${pageKey}`);
    } else {
      logger.error(`页面配置不存在: ${pageKey}`);
      throw new Error(`页面配置不存在: ${pageKey}`);
    }
  }

  /**
   * 获取页面配置（内存读取，无需文件IO）
   * @param {string} pageKey 页面唯一标识
   * @returns {Object|null} 配置对象（含urlRegex、elements等）
   */
  getPageConfig(pageKey) {
    return this.pageConfigs.get(pageKey) || null;
  }

  /**
   * 通过URL自动识别页面配置（内存匹配，高效）
   * @param {string} url 页面URL
   * @returns {Object|null} 匹配的配置（含pageKey）
   */
  getConfigByUrl(url) {
    for (const [pageKey, config] of this.pageConfigs) {
      if (config.urlRegex && config.urlRegex.test(url)) {
        return { pageKey, ...config };
      }
    }
    return null;
  }

  /**
   * 获取所有页面标识
   * @returns {string[]} 页面Key列表
   */
  getAllPageKeys() {
    return Array.from(this.pageConfigs.keys());
  }

  /**
   * 获取所有页面配置
   * @returns {Object} 所有配置的对象形式 { pageKey: config }
   */
  getAllPageConfigs() {
    const configs = {};
    for (const [pageKey, config] of this.pageConfigs) {
      configs[pageKey] = {
        urlPattern: config.urlPattern,
        elements: config.elements,
        // 添加一些元数据
        createdAt: config.createdAt || new Date().toISOString(),
        updatedAt: config.updatedAt || new Date().toISOString()
      };
    }
    return configs;
  }
}

module.exports = PageConfigStore;
