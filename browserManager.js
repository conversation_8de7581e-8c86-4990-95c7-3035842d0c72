const puppeteer = require('puppeteer');
const { v4: uuidv4 } = require('uuid');
const logger = require('./logger');

class BrowserManager {
  constructor() {
    // 默认超时时间：3分钟（180000毫秒）
    this.DEFAULT_TIMEOUT = 3 * 60 * 1000;

    // 存储浏览器实例: { browserId: { browser, page, proxy, timeoutId, lastActivity } }
    this.browsers = new Map();

    // 启动全局定期检查，确保超时实例被关闭（双重保险）
    this.startGlobalChecker();
  }

  /**
   * 创建新的浏览器实例
   * @param {Object} options 配置选项
   * @param {string} options.proxy 代理地址（可选）
   * @param {number} options.timeout 超时时间（毫秒，可选）
   * @returns {string} 浏览器实例ID
   */
  async createBrowser({ proxy, timeout } = {}) {
    const browserId = uuidv4();
    const effectiveTimeout = timeout || this.DEFAULT_TIMEOUT;

    // 解析代理信息
    let proxyServer, proxyAuth;
    if (proxy) {
      proxyServer = proxy;
      // 检查是否有认证信息
      const proxyMatch = proxy.match(/^([^:]+):\/\/([^:]+):([^@]+)@(.*)$/);
      if (proxyMatch) {
        const [, protocol, user, pass, hostPort] = proxyMatch;
        proxyServer = `${protocol}://${hostPort}`;
        proxyAuth = `${user}:${pass}`;
      }
    }

    // 启动浏览器
    const launchOptions = {
      // headless: 'new', // 无头
      headless: false, // 有头
      devtools: true,  // 自动打开开发者工具（可选，方便调试）
      slowMo: 100,     // 每个操作延迟100毫秒（可选，方便观察步骤）
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        ...(proxyServer ? [`--proxy-server=${proxyServer}`] : [])
      ],
      defaultViewport: { width: 1280, height: 720 },
      // executablePath: process.env.PUPPETEER_EXECUTABLE_PATH ||
      //   path.join(
      //     process.pkg ? process.cwd() : __dirname,
      //     'puppeteer/.local-chromium/linux-123456/chrome-linux/chrome'  // 替换为实际路径
      //   )
    };

    const browser = await puppeteer.launch(launchOptions);
    const pages = await browser.pages();
    const page = pages[0];

    // 设置代理认证（如果需要）
    if (proxyAuth) {
      await page.authenticate({
        username: proxyAuth.split(':')[0],
        password: proxyAuth.split(':')[1]
      });
    }

    // 设置超时计时器
    const timeoutId = setTimeout(
      () => this.closeBrowser(browserId, true),
      effectiveTimeout
    );

    // 存储实例信息
    this.browsers.set(browserId, {
      browser,
      page,
      proxy: proxy || null,
      timeoutId,
      timeout: effectiveTimeout,
      lastActivity: Date.now()
    });

    console.log(`Browser ${browserId} created (timeout: ${effectiveTimeout/1000}s)`);
    logger.info(`Browser ${browserId} created (timeout: ${effectiveTimeout/1000}s)`);
    return browserId;
  }

  /**
   * 获取浏览器页面实例，并更新活动时间
   * @param {string} browserId 浏览器实例ID
   * @returns {Promise<Page>} Puppeteer页面实例
   */
  async getPage(browserId) {
    const instance = this.browsers.get(browserId);

    if (!instance) {
      logger.error(`Browser instance ${browserId} not found`);
      throw new Error(`Browser instance ${browserId} not found`);
    }

    // 更新活动时间并重置超时计时器
    this.updateActivity(browserId);

    return instance.page;
  }

  /**
   * 更新浏览器实例的活动时间，重置超时计时器
   * @param {string} browserId 浏览器实例ID
   */
  updateActivity(browserId) {
    const instance = this.browsers.get(browserId);

    if (!instance) {
      return;
    }

    // 清除旧的超时计时器
    clearTimeout(instance.timeoutId);

    // 更新活动时间
    instance.lastActivity = Date.now();

    // 设置新的超时计时器
    instance.timeoutId = setTimeout(
      () => this.closeBrowser(browserId, true),
      instance.timeout
    );
  }

  /**
   * 关闭浏览器实例
   * @param {string} browserId 浏览器实例ID
   * @param {boolean} isTimeout 是否因超时关闭
   */
  async closeBrowser(browserId, isTimeout = false) {
    const instance = this.browsers.get(browserId);

    if (!instance) {
      return;
    }

    try {
      // 关闭浏览器
      await instance.browser.close();

      // 清除超时计时器
      clearTimeout(instance.timeoutId);

      // 从映射中移除
      this.browsers.delete(browserId);

      console.log(`Browser ${browserId} closed ${isTimeout ? 'due to timeout' : 'by request'}`);
      logger.info(`Browser ${browserId} closed ${isTimeout ? 'due to timeout' : 'by request'}`);
    } catch (error) {
      console.error(`Error closing browser ${browserId}:`, error);
      logger.error(`Error closing browser ${browserId}: ${error}`);
    }
  }

  /**
   * 获取当前活跃的浏览器实例数量
   * @returns {number} 活跃实例数量
   */
  getActiveCount() {
    return this.browsers.size;
  }

  /**
   * 获取活跃浏览器实例列表
   * @returns {Array} 浏览器实例信息列表
   */
  getBrowserList() {
    const browserList = [];
    this.browsers.forEach((instance, browserId) => {
      browserList.push({
        browserId,
        proxy: instance.proxy,
        timeout: instance.timeout,
        lastActivity: instance.lastActivity,
        createdAt: new Date(Date.now() - (Date.now() - instance.lastActivity)).toISOString()
      });
    });
    return browserList;
  }

  /**
   * 启动全局定期检查，确保超时实例被关闭
   */
  startGlobalChecker() {
    // 每30秒检查一次
    setInterval(() => {
      const now = Date.now();

      this.browsers.forEach((instance, browserId) => {
        const elapsed = now - instance.lastActivity;
        if (elapsed > instance.timeout) {
          console.log(`Global checker found timed out browser ${browserId}`);
          logger.info(`Global checker found timed out browser ${browserId}`);
          this.closeBrowser(browserId, true);
        }
      });
    }, 30 * 1000);
  }
}

module.exports = { BrowserManager };
