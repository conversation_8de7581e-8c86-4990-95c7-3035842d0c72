{"name": "dynamic-browser-control", "version": "1.0.0", "description": "Remote browser control system with dynamic proxy and timeout", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"body-parser": "^1.20.2", "express": "^4.18.2", "puppeteer": "^24.18.0", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}, "bin": "server.js", "pkg": {"targets": ["node18-linux-x64", "node18-win-x64", "node18-macos-x64"], "outputPath": "dist", "assets": ["node_modules/puppeteer/**/*", "!node_modules/puppeteer/.local-chromium/**/*"], "ignore": ["**/*.d.ts", "node_modules/typed-query-selector/**/*"]}}