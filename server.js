const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');
const { BrowserManager } = require('./browserManager');
const StepExecutor = require('./stepExecutor');
const PageConfigStore = require('./pageConfigStore');
const logger = require('./logger');

// 初始化应用
const app = express();
app.use(bodyParser.json());

// 添加静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 初始化核心组件
const browserManager = new BrowserManager();
const pageConfigStore = new PageConfigStore();
const stepExecutor = new StepExecutor(browserManager, pageConfigStore);

// 创建浏览器实例（支持动态代理）
app.post('/api/browser/create', async (req, res) => {
  try {
    const { proxy, timeout } = req.body;
    const browserId = await browserManager.createBrowser({
      proxy,
      timeout
    });

    res.json({
      success: true,
      browserId,
      message: `Browser instance created with ${proxy ? 'proxy' : 'no proxy'}`
    });
  } catch (error) {
    logger.error('创建浏览器实例失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取当前活跃浏览器数量
app.get('/api/browser/count', (req, res) => {
  res.json({
    success: true,
    activeCount: browserManager.getActiveCount()
  });
});

// 获取活跃浏览器列表
app.get('/api/browser/list', (req, res) => {
  try {
    const browserList = browserManager.getBrowserList();
    res.json({
      success: true,
      browsers: browserList
    });
  } catch (error) {
    logger.error('获取浏览器列表失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 关闭浏览器实例
app.post('/api/browser/close', async (req, res) => {
  try {
    const { browserId } = req.body;

    if (!browserId) {
      return res.status(400).json({ success: false, error: 'browserId is required' });
    }

    await browserManager.closeBrowser(browserId);
    res.json({ success: true, message: `Browser ${browserId} closed` });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 添加页面配置
app.post('/api/page-config/add', async (req, res) => {
  try {
    const { pageKey, urlPattern, elements } = req.body;
    // 调用异步的addPageConfig（自动持久化）
    await pageConfigStore.addPageConfig(pageKey, urlPattern, elements);
    res.json({ success: true, message: `页面配置${pageKey}添加成功` });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取所有页面配置
app.get('/api/page-config/list', (req, res) => {
  try {
    const configs = pageConfigStore.getAllPageConfigs();
    res.json({
      success: true,
      configs: configs
    });
  } catch (error) {
    logger.error('获取页面配置列表失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 更新页面配置
app.put('/api/page-config/update', async (req, res) => {
  try {
    const { pageKey, urlPattern, elements } = req.body;

    if (!pageKey) {
      return res.status(400).json({ success: false, error: 'pageKey is required' });
    }

    // 检查配置是否存在
    const existingConfig = pageConfigStore.getPageConfig(pageKey);
    if (!existingConfig) {
      return res.status(404).json({ success: false, error: `页面配置${pageKey}不存在` });
    }

    // 更新配置（实际上addPageConfig方法已经支持更新）
    await pageConfigStore.addPageConfig(pageKey, urlPattern, elements);
    res.json({ success: true, message: `页面配置${pageKey}更新成功` });
  } catch (error) {
    logger.error('更新页面配置失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 删除页面配置
app.post('/api/page-config/delete', async (req, res) => {
  try {
    const { pageKey } = req.body;
    await pageConfigStore.removePageConfig(pageKey);
    res.json({ success: true, message: `页面配置${pageKey}删除成功` });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 执行下单步骤
app.post('/api/executor/run-order-steps', async (req, res) => {
  try {
    const { browserId, pageKey, phoneNumber, verifyCode, targetUrl, steps } = req.body;
    console.log(browserId, pageKey, phoneNumber, verifyCode, targetUrl, steps);
    if (!browserId || !pageKey || !steps) {
      return res.status(400).json({
        success: false,
        error: 'browserId, pageKey and steps are required'
      });
    }
    // 执行固定步骤
    const result = await stepExecutor.executeOrderSteps(
      browserId,
      pageKey,
      targetUrl,
      phoneNumber,
      verifyCode,
      steps
    );

    res.json(result);
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 启动服务器
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Default browser timeout: ${browserManager.DEFAULT_TIMEOUT / 1000} seconds`);
  logger.info('服务启动成功', { port: PORT, env: process.env.NODE_ENV || 'development' });
  logger.info(`浏览器默认超时时间 ${browserManager.DEFAULT_TIMEOUT / 1000} 秒`);
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
