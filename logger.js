const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');

// 定义日志存储目录（确保可写，打包后兼容）
const LOG_DIR = process.env.LOG_DIR 
  ? path.resolve(process.env.LOG_DIR) 
  : (process.pkg ? path.join(process.cwd(), 'logs') : path.join(__dirname, 'logs'));

// 日志格式配置
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), // 时间戳
  winston.format.json(), // JSON格式（便于解析）
  // 也可使用简单文本格式（适合人工阅读）：
  // winston.format.printf(({ timestamp, level, message, ...meta }) => {
  //   return `[${timestamp}] ${level}: ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`;
  // })
);

// 按日期分割的文件传输配置（info级别及以上）
const infoFileTransport = new DailyRotateFile({
  filename: path.join(LOG_DIR, 'info-%DATE%.log'), // 文件名格式：info-2024-09-02.log
  datePattern: 'YYYY-MM-DD', // 按天分割
  maxSize: '20m', // 单个文件最大20MB
  maxFiles: '14d', // 保留14天的日志
  level: 'info', // 记录info及以上级别（info/warn/error）
});

// 错误日志单独存储（仅error级别）
const errorFileTransport = new DailyRotateFile({
  filename: path.join(LOG_DIR, 'error-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  maxSize: '10m',
  maxFiles: '30d', // 错误日志保留更久（30天）
  level: 'error', // 只记录error级别
});

// 控制台输出配置（开发环境用）
const consoleTransport = new winston.transports.Console({
  level: 'debug', // 控制台显示debug及以上级别（更详细）
  format: winston.format.combine(
    winston.format.colorize(), // 彩色输出
    winston.format.timestamp({ format: 'HH:mm:ss' }),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      return `[${timestamp}] ${level}: ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`;
    })
  ),
});

// 创建日志实例
const logger = winston.createLogger({
  levels: winston.config.npm.levels, // 标准日志级别：error > warn > info > verbose > debug > silly
  defaultMeta: { service: 'browser-control' }, // 每条日志附加的默认元数据
  format: logFormat,
  transports: [
    infoFileTransport,
    errorFileTransport,
    // 生产环境可移除控制台输出
    ...(process.env.NODE_ENV === 'production' ? [] : [consoleTransport])
  ],
});

// 导出日志实例
module.exports = logger;
