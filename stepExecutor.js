const logger = require('./logger');

class StepExecutor {
  /**
   * 初始化步骤执行器
   * @param {BrowserManager} browserManager 浏览器管理器实例
   * @param {PageConfigStore} pageConfigStore 页面配置存储实例
   */
  constructor(browserManager, pageConfigStore) {
    this.browserManager = browserManager;
    this.pageConfigStore = pageConfigStore;
  }

  /**
   * 执行下单的固定步骤
   * 1. 输入手机号
   * 2. 点击获取验证码
   * 3. 回填验证码
   * 4. 点击下单
   *
   * @param {string} browserId 浏览器实例ID
   * @param {string} pageKey 页面标识
   * @param {string} url 要访问的页面URL
   * @param {string} phoneNumber 手机号
   * @param {string} verifyCode 验证码
   * @param {string} steps 步骤 1.页面打开, (勾选协议), 输入手机号, 点击获取验证码 2.填写验证码, 点击下单, (二次确认)
   * @returns {Object} 执行结果
   */
  async executeOrderSteps(browserId, pageKey, url, phoneNumber, verifyCode, steps) {
    try {
      // 获取页面配置
      const pageConfig = this.pageConfigStore.getPageConfig(pageKey);
      if (!pageConfig) {
        return {
          success: false,
          error: `No configuration found for pageKey: ${pageKey}`
        };
      }

      // 验证必要的元素配置是否存在
      const requiredElements = ['inputPhone', 'getCodeButton', 'codeInput', 'orderButton'];
      const missingElements = requiredElements.filter(elem => !pageConfig.elements[elem]);

      if (missingElements.length > 0) {
        logger.error(`Missing element configurations for: ${missingElements.join(', ')}`);
        return {
          success: false,
          error: `Missing element configurations for: ${missingElements.join(', ')}`
        };
      }

      // 获取页面实例
      const page = await this.browserManager.getPage(browserId);

      switch(steps) {
        case 1:
          // 步骤1: 导航到目标页面
          console.log(`Navigating to ${url}`);
          logger.info(`Navigating to ${url}`);
          await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 });
          if (pageConfig.elements.agreementRadio) {
            // 点击勾选协议
            console.log('Step: Clicking agreement radio');
            logger.info('Step: Clicking agreement radio');
            const agreementButtonElement = this.getElementSelector(pageConfig.elements.agreementRadio);
            await page.waitForSelector(agreementButtonElement.selector, { timeout: 30000 });
            await page.click(agreementButtonElement.selector);
          }
          // 步骤2: 输入手机号
          console.log('Step 1: Entering phone number');
          logger.info('Step 1: Entering phone number');
          const phoneElement = this.getElementSelector(pageConfig.elements.inputPhone);
          await page.waitForSelector(phoneElement.selector, { timeout: 30000 });
          await page.focus(phoneElement.selector);
          await page.keyboard.type(phoneNumber, { delay: 10 });
          console.log('Step 2: Clicking get verification code button');
          logger.info('Step 2: Clicking get verification code button');
          const codeButtonElement = this.getElementSelector(pageConfig.elements.getCodeButton);
          await page.waitForSelector(codeButtonElement.selector, { timeout: 30000 });
          await page.click(codeButtonElement.selector);
          break;
        case 2:
          // 步骤4: 输入验证码
          console.log('Step 3: Entering verification code');
          logger.info('Step 3: Entering verification code');
          const codeInputElement = this.getElementSelector(pageConfig.elements.codeInput);
          await page.waitForSelector(codeInputElement.selector, { timeout: 30000 });
          await page.focus(codeInputElement.selector);
          await page.keyboard.type(verifyCode, { delay: 100 });

          console.log('Step 4: Clicking order button');
          logger.info('Step 4: Clicking order button');
          const orderButtonElement = this.getElementSelector(pageConfig.elements.orderButton);
          await page.waitForSelector(orderButtonElement.selector, { timeout: 30000 });
          await page.click(orderButtonElement.selector);

          if (pageConfig.elements.confirmationButton) {
            console.log('Step: Clicking confirmation button');
            logger.info('Step: Clicking confirmation button');
            const confirmationButtonElement = this.getElementSelector(pageConfig.elements.confirmationButton);
            await page.waitForSelector(confirmationButtonElement.selector, { timeout: 30000 });
            await page.click(agreementButtonElement.selector);
          }

            break;
      }

      return {
        success: true,
        message: `Order steps ${steps} executed successfully`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error executing order steps:', error);
      logger.error('Error executing order steps:', error);
      return {
        success: false,
        error: error.message,
        step: error.step || 'unknown'
      };
    }
  }

  /**
   * 格式化元素选择器
   * @param {Object} elementConfig 元素配置
   * @returns {Object} 格式化的选择器信息
   */
  getElementSelector(elementConfig) {
    // 默认使用CSS选择器
    const type = elementConfig.type || 'css';
    const selector = elementConfig.selector;

    if (!selector) {
      throw new Error('Element selector is required');
    }

    return { type, selector };
  }
}

module.exports = StepExecutor;
