/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.header h1 {
    color: #4a5568;
    font-size: 1.8rem;
    font-weight: 600;
}

.header h1 i {
    color: #667eea;
    margin-right: 10px;
}

.status-bar {
    display: flex;
    gap: 20px;
    align-items: center;
}

.status-indicator {
    color: #48bb78;
    font-weight: 500;
}

.status-indicator i {
    animation: pulse 2s infinite;
}

.browser-count {
    background: #667eea;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 主要内容区域 */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    background: #f7fafc;
    border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    color: #718096;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #edf2f7;
    color: #4a5568;
}

.tab-btn.active {
    color: #667eea;
    background: white;
    border-bottom-color: #667eea;
}

.tab-btn i {
    margin-right: 8px;
}

/* 标签页内容 */
.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

/* 面板样式 */
.panel {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.panel h2 {
    color: #2d3748;
    font-size: 1.3rem;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f7fafc;
}

.panel h2 i {
    color: #667eea;
    margin-right: 10px;
}

/* 表单样式 */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-group input,
.form-group select {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group small {
    color: #718096;
    font-size: 0.8rem;
    margin-top: 5px;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    min-height: 48px;
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #718096;
    color: white;
}

.btn-secondary:hover {
    background: #4a5568;
    transform: translateY(-2px);
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
    transform: translateY(-2px);
}

.btn-danger {
    background: #f56565;
    color: white;
}

.btn-danger:hover {
    background: #e53e3e;
    transform: translateY(-2px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 元素配置样式 */
.element-config {
    display: grid;
    gap: 15px;
}

.element-item {
    padding: 15px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #f7fafc;
}

.element-item label {
    display: block;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 10px;
}

.selector-group {
    display: flex;
    gap: 10px;
}

.selector-type {
    width: 100px;
    flex-shrink: 0;
}

.selector-input {
    flex: 1;
}

/* 表单按钮组 */
.form-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.form-buttons .btn {
    flex: 1;
    min-width: 150px;
}

/* 步骤按钮 */
.step-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.step-buttons .btn {
    flex: 1;
    min-width: 200px;
}

/* 列表样式 */
.browser-list,
.config-list {
    display: grid;
    gap: 15px;
}

.browser-item,
.config-item {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.browser-item:hover,
.config-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.1);
}

.item-info h3 {
    color: #2d3748;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.item-info p {
    color: #718096;
    font-size: 0.9rem;
    margin: 2px 0;
}

.item-actions {
    display: flex;
    gap: 10px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 1.1rem;
}

/* 执行日志 */
.execution-log {
    max-height: 400px;
    overflow-y: auto;
    background: #1a202c;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
}

.log-entry {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 4px solid transparent;
}

.log-entry.success {
    background: rgba(72, 187, 120, 0.1);
    border-left-color: #48bb78;
}

.log-entry.error {
    background: rgba(245, 101, 101, 0.1);
    border-left-color: #f56565;
}

.log-entry.info {
    background: rgba(102, 126, 234, 0.1);
    border-left-color: #667eea;
}

.log-timestamp {
    color: #a0aec0;
    font-size: 0.8rem;
}

/* 通知消息 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #48bb78;
}

.notification.error {
    background: #f56565;
}

.notification.info {
    background: #667eea;
}

/* 加载指示器 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    flex-direction: column;
}

.loading.show {
    display: flex;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading p {
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .status-bar {
        flex-direction: column;
        gap: 10px;
    }

    .tab-navigation {
        flex-direction: column;
    }

    .tab-btn {
        padding: 12px 15px;
        text-align: left;
    }

    .tab-content {
        padding: 20px 15px;
    }

    .panel {
        padding: 20px 15px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .step-buttons {
        flex-direction: column;
    }

    .step-buttons .btn {
        min-width: auto;
    }

    .browser-item,
    .config-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .item-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.5rem;
    }

    .panel h2 {
        font-size: 1.1rem;
    }

    .btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .selector-group {
        flex-direction: column;
    }

    .selector-type {
        width: 100%;
    }
}
