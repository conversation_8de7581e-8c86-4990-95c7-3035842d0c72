<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器控制系统</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <h1><i class="fas fa-browser"></i> 浏览器控制系统</h1>
            <div class="status-bar">
                <span id="status-indicator" class="status-indicator">
                    <i class="fas fa-circle"></i> 系统运行中
                </span>
                <span id="browser-count" class="browser-count">活跃浏览器: 0</span>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 标签页导航 -->
            <div class="tab-navigation">
                <button class="tab-btn active" data-tab="browser-management">
                    <i class="fas fa-desktop"></i> 浏览器管理
                </button>
                <button class="tab-btn" data-tab="page-config">
                    <i class="fas fa-cog"></i> 页面配置
                </button>
                <button class="tab-btn" data-tab="step-execution">
                    <i class="fas fa-play"></i> 步骤执行
                </button>
            </div>

            <!-- 浏览器管理面板 -->
            <div id="browser-management" class="tab-content active">
                <div class="panel">
                    <h2><i class="fas fa-plus-circle"></i> 创建浏览器实例</h2>
                    <form id="create-browser-form" class="form-grid">
                        <div class="form-group">
                            <label for="proxy-input">代理地址 (可选)</label>
                            <input type="text" id="proxy-input" placeholder="*********************:port">
                            <small>格式: http://host:port 或 *********************:port</small>
                        </div>
                        <div class="form-group">
                            <label for="timeout-input">超时时间 (秒)</label>
                            <input type="number" id="timeout-input" value="180" min="30" max="3600">
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-rocket"></i> 创建浏览器
                        </button>
                    </form>
                </div>

                <div class="panel">
                    <h2><i class="fas fa-list"></i> 活跃浏览器实例</h2>
                    <div class="browser-list" id="browser-list">
                        <div class="empty-state">
                            <i class="fas fa-browser"></i>
                            <p>暂无活跃的浏览器实例</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 页面配置面板 -->
            <div id="page-config" class="tab-content">
                <div class="panel">
                    <h2><i class="fas fa-plus-circle"></i> 添加页面配置</h2>
                    <form id="add-config-form" class="form-grid">
                        <div class="form-group">
                            <label for="page-key-input">页面标识 (pageKey)</label>
                            <input type="text" id="page-key-input" placeholder="例如: pageA" required>
                        </div>
                        <div class="form-group">
                            <label for="url-pattern-input">URL模式 (可选)</label>
                            <input type="text" id="url-pattern-input" placeholder="例如: https://example.com/page">
                        </div>
                        <div class="form-group full-width">
                            <label>元素选择器配置</label>
                            <div class="element-config" id="element-config">
                                <div class="element-item">
                                    <label>手机号输入框 (inputPhone)</label>
                                    <div class="selector-group">
                                        <select class="selector-type">
                                            <option value="css">CSS</option>
                                            <option value="xpath">XPath</option>
                                        </select>
                                        <input type="text" class="selector-input" placeholder="选择器" data-element="inputPhone">
                                    </div>
                                </div>
                                <div class="element-item">
                                    <label>获取验证码按钮 (getCodeButton)</label>
                                    <div class="selector-group">
                                        <select class="selector-type">
                                            <option value="css">CSS</option>
                                            <option value="xpath">XPath</option>
                                        </select>
                                        <input type="text" class="selector-input" placeholder="选择器" data-element="getCodeButton">
                                    </div>
                                </div>
                                <div class="element-item">
                                    <label>验证码输入框 (codeInput)</label>
                                    <div class="selector-group">
                                        <select class="selector-type">
                                            <option value="css">CSS</option>
                                            <option value="xpath">XPath</option>
                                        </select>
                                        <input type="text" class="selector-input" placeholder="选择器" data-element="codeInput">
                                    </div>
                                </div>
                                <div class="element-item">
                                    <label>下单按钮 (orderButton)</label>
                                    <div class="selector-group">
                                        <select class="selector-type">
                                            <option value="css">CSS</option>
                                            <option value="xpath">XPath</option>
                                        </select>
                                        <input type="text" class="selector-input" placeholder="选择器" data-element="orderButton">
                                    </div>
                                </div>
                                <div class="element-item">
                                    <label>协议勾选框 (agreementRadio) - 可选</label>
                                    <div class="selector-group">
                                        <select class="selector-type">
                                            <option value="css">CSS</option>
                                            <option value="xpath">XPath</option>
                                        </select>
                                        <input type="text" class="selector-input" placeholder="选择器" data-element="agreementRadio">
                                    </div>
                                </div>
                                <div class="element-item">
                                    <label>确认按钮 (confirmationButton) - 可选</label>
                                    <div class="selector-group">
                                        <select class="selector-type">
                                            <option value="css">CSS</option>
                                            <option value="xpath">XPath</option>
                                        </select>
                                        <input type="text" class="selector-input" placeholder="选择器" data-element="confirmationButton">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-buttons">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存配置
                            </button>
                            <button type="button" class="btn btn-secondary" id="cancel-edit-btn" style="display: none;">
                                <i class="fas fa-times"></i> 取消编辑
                            </button>
                        </div>
                    </form>
                </div>

                <div class="panel">
                    <h2><i class="fas fa-list"></i> 页面配置列表</h2>
                    <div class="config-list" id="config-list">
                        <div class="empty-state">
                            <i class="fas fa-cog"></i>
                            <p>暂无页面配置</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 步骤执行面板 -->
            <div id="step-execution" class="tab-content">
                <div class="panel">
                    <h2><i class="fas fa-play-circle"></i> 执行自动化步骤</h2>
                    <form id="execute-steps-form" class="form-grid">
                        <div class="form-group">
                            <label for="browser-select">选择浏览器实例</label>
                            <select id="browser-select" required>
                                <option value="">请选择浏览器实例</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="page-config-select">选择页面配置</label>
                            <select id="page-config-select" required>
                                <option value="">请选择页面配置</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="target-url-input">目标URL</label>
                            <input type="url" id="target-url-input" placeholder="https://example.com/page" required>
                        </div>
                        <div class="form-group">
                            <label for="phone-number-input">手机号</label>
                            <input type="tel" id="phone-number-input" placeholder="13800138000" required>
                        </div>
                        <div class="form-group">
                            <label for="verify-code-input">验证码</label>
                            <input type="text" id="verify-code-input" placeholder="验证码">
                        </div>
                        <div class="form-group full-width">
                            <div class="step-buttons">
                                <button type="button" class="btn btn-secondary" id="step1-btn">
                                    <i class="fas fa-step-forward"></i> 步骤1: 发送验证码
                                </button>
                                <button type="button" class="btn btn-success" id="step2-btn">
                                    <i class="fas fa-check-circle"></i> 步骤2: 下单
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="panel">
                    <h2><i class="fas fa-terminal"></i> 执行结果</h2>
                    <div class="execution-log" id="execution-log">
                        <div class="empty-state">
                            <i class="fas fa-info-circle"></i>
                            <p>执行结果将在这里显示</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 通知消息 -->
        <div id="notification" class="notification"></div>

        <!-- 加载指示器 -->
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>处理中...</p>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
