// 浏览器控制系统前端应用
class BrowserControlApp {
    constructor() {
        this.activeBrowsers = new Map();
        this.pageConfigs = new Map();
        this.init();
    }

    // 初始化应用
    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.startPeriodicUpdates();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 创建浏览器表单
        document.getElementById('create-browser-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createBrowser();
        });

        // 添加页面配置表单
        document.getElementById('add-config-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addPageConfig();
        });

        // 步骤执行按钮
        document.getElementById('step1-btn').addEventListener('click', () => {
            this.executeStep(1);
        });

        document.getElementById('step2-btn').addEventListener('click', () => {
            this.executeStep(2);
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + 数字键切换标签页
            if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '3') {
                e.preventDefault();
                const tabs = ['browser-management', 'page-config', 'step-execution'];
                this.switchTab(tabs[parseInt(e.key) - 1]);
            }
        });
    }

    // 加载初始数据
    async loadInitialData() {
        await Promise.all([
            this.loadBrowserCount(),
            this.loadPageConfigs(),
            this.updateBrowserSelects()
        ]);
    }

    // 开始定期更新
    startPeriodicUpdates() {
        // 每5秒更新浏览器数量和列表
        setInterval(() => {
            this.loadBrowserCount();
            // 如果在浏览器管理或步骤执行标签页，更新浏览器列表
            const activeTab = document.querySelector('.tab-content.active');
            if (activeTab && (activeTab.id === 'browser-management' || activeTab.id === 'step-execution')) {
                this.updateBrowserSelects();
            }
        }, 5000);
    }

    // 切换标签页
    switchTab(tabId) {
        // 移除所有活跃状态
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // 激活选中的标签页
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
        document.getElementById(tabId).classList.add('active');

        // 根据标签页加载相应数据
        if (tabId === 'step-execution') {
            this.updateBrowserSelects();
            this.updatePageConfigSelects();
        }
    }

    // API调用封装
    async apiCall(url, options = {}, retries = 1) {
        // this.showLoading();

        for (let attempt = 0; attempt <= retries; attempt++) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || `HTTP ${response.status}`);
                }

                return data;
            } catch (error) {
                if (attempt === retries) {
                    this.showNotification(error.message, 'error');
                    throw error;
                } else {
                    // 等待1秒后重试
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            } finally {
                // this.hideLoading();
            }
        }

        // this.hideLoading();
    }

    // 创建浏览器实例
    async createBrowser() {
        const proxy = document.getElementById('proxy-input').value.trim();
        const timeoutInput = document.getElementById('timeout-input').value;

        // 验证超时时间
        if (!timeoutInput || isNaN(timeoutInput) || parseInt(timeoutInput) < 30) {
            this.showNotification('超时时间必须是大于30秒的数字', 'error');
            return;
        }

        const timeout = parseInt(timeoutInput) * 1000;

        // 验证代理格式（如果提供）
        if (proxy && !this.validateProxyFormat(proxy)) {
            this.showNotification('代理格式不正确，请使用 http://host:port 或 *********************:port 格式', 'error');
            return;
        }

        const requestBody = { timeout };
        if (proxy) {
            requestBody.proxy = proxy;
        }

        try {
            const result = await this.apiCall('/api/browser/create', {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            this.showNotification(result.message, 'success');

            // 清空表单
            document.getElementById('create-browser-form').reset();
            document.getElementById('timeout-input').value = '180';

            // 更新浏览器列表和计数
            this.loadBrowserCount();
            this.updateBrowserSelects();

        } catch (error) {
            console.error('创建浏览器失败:', error);
        }
    }

    // 关闭浏览器实例
    async closeBrowser(browserId) {
        if (!confirm('确定要关闭这个浏览器实例吗？')) {
            return;
        }

        try {
            const result = await this.apiCall('/api/browser/close', {
                method: 'POST',
                body: JSON.stringify({ browserId })
            });

            this.showNotification(result.message, 'success');
            this.loadBrowserCount();
            this.updateBrowserSelects();

        } catch (error) {
            console.error('关闭浏览器失败:', error);
        }
    }

    // 加载浏览器数量
    async loadBrowserCount() {
        try {
            const result = await this.apiCall('/api/browser/count');
            document.getElementById('browser-count').textContent = `活跃浏览器: ${result.activeCount}`;
        } catch (error) {
            console.error('加载浏览器数量失败:', error);
        }
    }

    // 添加页面配置
    async addPageConfig() {
        const pageKey = document.getElementById('page-key-input').value.trim();
        const urlPattern = document.getElementById('url-pattern-input').value.trim();

        if (!pageKey) {
            this.showNotification('页面标识不能为空', 'error');
            return;
        }

        // 收集元素配置
        const elements = {};
        const elementItems = document.querySelectorAll('.element-item');

        elementItems.forEach(item => {
            const input = item.querySelector('.selector-input');
            const select = item.querySelector('.selector-type');
            const elementName = input.dataset.element;
            const selector = input.value.trim();

            if (selector) {
                elements[elementName] = {
                    selector: selector,
                    type: select.value
                };
            }
        });

        // 验证必需的元素
        const requiredElements = ['inputPhone', 'getCodeButton', 'codeInput', 'orderButton'];
        const missingElements = requiredElements.filter(elem => !elements[elem]);

        if (missingElements.length > 0) {
            this.showNotification(`缺少必需的元素配置: ${missingElements.join(', ')}`, 'error');
            return;
        }

        const requestBody = {
            pageKey,
            elements
        };

        if (urlPattern) {
            requestBody.urlPattern = urlPattern;
        }

        try {
            const result = await this.apiCall('/api/page-config/add', {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            this.showNotification(result.message, 'success');

            // 保存到本地存储
            this.pageConfigs.set(pageKey, {
                ...requestBody,
                createdAt: new Date().toLocaleString()
            });
            this.savePageConfigsToStorage();

            // 清空表单
            document.getElementById('add-config-form').reset();

            // 重新渲染页面配置
            this.renderPageConfigs();
            this.updatePageConfigSelects();

        } catch (error) {
            console.error('添加页面配置失败:', error);
        }
    }

    // 删除页面配置
    async deletePageConfig(pageKey) {
        if (!confirm(`确定要删除页面配置 "${pageKey}" 吗？`)) {
            return;
        }

        try {
            const result = await this.apiCall('/api/page-config/delete', {
                method: 'POST',
                body: JSON.stringify({ pageKey })
            });

            this.showNotification(result.message, 'success');

            // 从本地存储中删除
            this.pageConfigs.delete(pageKey);
            this.savePageConfigsToStorage();

            this.renderPageConfigs();
            this.updatePageConfigSelects();

        } catch (error) {
            console.error('删除页面配置失败:', error);
        }
    }

    // 加载页面配置
    async loadPageConfigs() {
        try {
            // 尝试从localStorage读取已保存的配置
            const savedConfigs = localStorage.getItem('pageConfigs');
            if (savedConfigs) {
                const configs = JSON.parse(savedConfigs);
                this.pageConfigs.clear();
                Object.entries(configs).forEach(([key, value]) => {
                    this.pageConfigs.set(key, value);
                });
            }
        } catch (error) {
            console.error('加载页面配置失败:', error);
        }

        this.renderPageConfigs();
    }

    // 渲染页面配置列表
    renderPageConfigs() {
        const configList = document.getElementById('config-list');

        if (this.pageConfigs.size === 0) {
            configList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-cog"></i>
                    <p>暂无页面配置</p>
                </div>
            `;
        } else {
            let html = '';
            this.pageConfigs.forEach((config, pageKey) => {
                html += `
                    <div class="config-item">
                        <div class="item-info">
                            <h3>${pageKey}</h3>
                            <p>URL模式: ${config.urlPattern || '未设置'}</p>
                            <p>元素数量: ${Object.keys(config.elements).length}</p>
                            <p>创建时间: ${config.createdAt || '未知'}</p>
                        </div>
                        <div class="item-actions">
                            <button class="btn btn-secondary" onclick="app.editPageConfig('${pageKey}')">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-danger" onclick="app.deletePageConfig('${pageKey}')">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                `;
            });
            configList.innerHTML = html;
        }
    }

    // 保存页面配置到localStorage
    savePageConfigsToStorage() {
        try {
            const configsObj = {};
            this.pageConfigs.forEach((value, key) => {
                configsObj[key] = value;
            });
            localStorage.setItem('pageConfigs', JSON.stringify(configsObj));
        } catch (error) {
            console.error('保存页面配置失败:', error);
        }
    }

    // 编辑页面配置
    editPageConfig(pageKey) {
        const config = this.pageConfigs.get(pageKey);
        if (!config) {
            this.showNotification('配置不存在', 'error');
            return;
        }

        // 切换到页面配置标签
        this.switchTab('page-config');

        // 填充表单
        document.getElementById('page-key-input').value = pageKey;
        document.getElementById('url-pattern-input').value = config.urlPattern || '';

        // 填充元素配置
        const elementItems = document.querySelectorAll('.element-item');
        elementItems.forEach(item => {
            const input = item.querySelector('.selector-input');
            const select = item.querySelector('.selector-type');
            const elementName = input.dataset.element;

            if (config.elements[elementName]) {
                input.value = config.elements[elementName].selector;
                select.value = config.elements[elementName].type;
            }
        });

        this.showNotification('配置已加载到表单，修改后点击保存', 'info');
    }

    // 执行步骤
    async executeStep(stepNumber) {
        const browserId = document.getElementById('browser-select').value;
        const pageKey = document.getElementById('page-config-select').value;
        const targetUrl = document.getElementById('target-url-input').value.trim();
        const phoneNumber = document.getElementById('phone-number-input').value.trim();
        const verifyCode = document.getElementById('verify-code-input').value.trim();

        // 验证必需字段
        if (!browserId) {
            this.showNotification('请选择浏览器实例', 'error');
            return;
        }

        if (!pageKey) {
            this.showNotification('请选择页面配置', 'error');
            return;
        }

        if (!targetUrl) {
            this.showNotification('请输入目标URL', 'error');
            return;
        }

        if (!this.validateUrlFormat(targetUrl)) {
            this.showNotification('请输入有效的URL格式', 'error');
            return;
        }

        if (!phoneNumber) {
            this.showNotification('请输入手机号', 'error');
            return;
        }

        if (!this.validatePhoneFormat(phoneNumber)) {
            this.showNotification('请输入有效的手机号格式', 'error');
            return;
        }

        if (stepNumber === 2 && !verifyCode) {
            this.showNotification('执行步骤2需要输入验证码', 'error');
            return;
        }

        const requestBody = {
            browserId,
            pageKey,
            targetUrl,
            phoneNumber,
            verifyCode,
            steps: stepNumber
        };

        try {
            const result = await this.apiCall('/api/executor/run-order-steps', {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            this.addLogEntry(`步骤${stepNumber}执行成功`, 'success', result);
            this.showNotification(`步骤${stepNumber}执行成功`, 'success');

        } catch (error) {
            this.addLogEntry(`步骤${stepNumber}执行失败: ${error.message}`, 'error');
            console.error('执行步骤失败:', error);
        }
    }

    // 更新浏览器选择下拉菜单
    async updateBrowserSelects() {
        try {
            const result = await this.apiCall('/api/browser/list');
            const select = document.getElementById('browser-select');
            select.innerHTML = '<option value="">请选择浏览器实例</option>';

            result.browsers.forEach(browser => {
                const option = document.createElement('option');
                option.value = browser.browserId;
                option.textContent = `${browser.browserId.substring(0, 8)}... (${browser.proxy || '无代理'})`;
                select.appendChild(option);
            });

            // 更新浏览器列表显示
            this.renderBrowserList(result.browsers);
        } catch (error) {
            console.error('获取浏览器列表失败:', error);
        }
    }

    // 渲染浏览器列表
    renderBrowserList(browsers) {
        const browserList = document.getElementById('browser-list');

        if (browsers.length === 0) {
            browserList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-browser"></i>
                    <p>暂无活跃的浏览器实例</p>
                </div>
            `;
        } else {
            let html = '';
            browsers.forEach(browser => {
                const createdTime = new Date(browser.lastActivity).toLocaleString();
                html += `
                    <div class="browser-item">
                        <div class="item-info">
                            <h3>${browser.browserId}</h3>
                            <p>代理: ${browser.proxy || '无代理'}</p>
                            <p>超时: ${browser.timeout / 1000}秒</p>
                            <p>最后活动: ${createdTime}</p>
                        </div>
                        <div class="item-actions">
                            <button class="btn btn-danger" onclick="app.closeBrowser('${browser.browserId}')">
                                <i class="fas fa-times"></i> 关闭
                            </button>
                        </div>
                    </div>
                `;
            });
            browserList.innerHTML = html;
        }
    }

    // 更新页面配置选择下拉菜单
    updatePageConfigSelects() {
        const select = document.getElementById('page-config-select');
        select.innerHTML = '<option value="">请选择页面配置</option>';

        this.pageConfigs.forEach((config, pageKey) => {
            const option = document.createElement('option');
            option.value = pageKey;
            option.textContent = pageKey;
            select.appendChild(option);
        });
    }

    // 添加日志条目
    addLogEntry(message, type = 'info', data = null) {
        const logContainer = document.getElementById('execution-log');

        // 如果是第一条日志，清除空状态
        if (logContainer.querySelector('.empty-state')) {
            logContainer.innerHTML = '';
        }

        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;

        let content = `<span class="log-timestamp">[${timestamp}]</span> ${message}`;
        if (data) {
            content += `\n${JSON.stringify(data, null, 2)}`;
        }

        logEntry.innerHTML = content;
        logContainer.appendChild(logEntry);

        // 滚动到底部
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        notification.textContent = message;
        notification.className = `notification ${type} show`;

        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }

    // 显示加载指示器
    showLoading() {
        document.getElementById('loading').classList.add('show');
    }

    // 隐藏加载指示器
    hideLoading() {
        document.getElementById('loading').classList.remove('show');
    }

    // 验证代理格式
    validateProxyFormat(proxy) {
        const proxyRegex = /^https?:\/\/(?:[\w-]+(?::[\w-]+)?@)?[\w.-]+:\d+$/;
        return proxyRegex.test(proxy);
    }

    // 验证URL格式
    validateUrlFormat(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    // 验证手机号格式
    validatePhoneFormat(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    }
}

// 初始化应用
const app = new BrowserControlApp();
