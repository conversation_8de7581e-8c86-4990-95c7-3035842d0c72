# 浏览器控制系统 - 前端界面

## 功能特性

### 1. 浏览器管理
- ✅ 创建浏览器实例（支持代理配置和超时设置）
- ✅ 显示活跃浏览器实例列表
- ✅ 手动关闭浏览器实例
- ✅ 实时更新浏览器数量

### 2. 页面配置管理
- ✅ 添加/编辑页面配置
- ✅ 可视化元素选择器编辑器（CSS/XPath）
- ✅ 页面配置列表展示和删除
- ✅ 本地存储配置数据

### 3. 步骤执行
- ✅ 选择浏览器实例和页面配置
- ✅ 输入手机号、验证码、目标URL
- ✅ 分步执行（步骤1：发送验证码，步骤2：下单）
- ✅ 实时显示执行结果和日志

### 4. 用户体验
- ✅ 响应式设计（支持桌面和移动端）
- ✅ 现代化UI设计
- ✅ 实时通知和反馈
- ✅ 加载指示器
- ✅ 错误处理机制

## 使用说明

### 启动服务
```bash
node server.js
```

### 访问界面
打开浏览器访问：`http://localhost:3000`

### 基本流程
1. **创建浏览器实例**
   - 在"浏览器管理"标签页中
   - 可选择配置代理和超时时间
   - 点击"创建浏览器"按钮

2. **配置页面元素**
   - 在"页面配置"标签页中
   - 输入页面标识和URL模式
   - 配置必需的元素选择器：
     - 手机号输入框 (inputPhone)
     - 获取验证码按钮 (getCodeButton)
     - 验证码输入框 (codeInput)
     - 下单按钮 (orderButton)
   - 可选配置：
     - 协议勾选框 (agreementRadio)
     - 确认按钮 (confirmationButton)

3. **执行自动化步骤**
   - 在"步骤执行"标签页中
   - 选择浏览器实例和页面配置
   - 输入目标URL和手机号
   - 执行步骤1：发送验证码
   - 输入验证码后执行步骤2：下单

## 技术实现

### 前端技术栈
- HTML5 + CSS3 + JavaScript (ES6+)
- Font Awesome 图标库
- 响应式设计
- 本地存储 (localStorage)

### API集成
- `/api/browser/create` - 创建浏览器实例
- `/api/browser/list` - 获取浏览器列表
- `/api/browser/close` - 关闭浏览器实例
- `/api/browser/count` - 获取浏览器数量
- `/api/page-config/add` - 添加页面配置
- `/api/page-config/delete` - 删除页面配置
- `/api/executor/run-order-steps` - 执行步骤

### 文件结构
```
public/
├── index.html      # 主页面
├── styles.css      # 样式文件
├── app.js          # JavaScript功能
└── README.md       # 说明文档
```

## 浏览器兼容性
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 移动端支持
- 响应式布局适配手机和平板
- 触摸友好的交互设计
- 优化的移动端表单体验

## 注意事项
1. 页面配置会保存在浏览器本地存储中
2. 浏览器实例信息通过API实时获取
3. 执行日志会在页面刷新后清空
4. 建议在现代浏览器中使用以获得最佳体验
