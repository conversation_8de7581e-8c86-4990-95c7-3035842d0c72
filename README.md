# 异地浏览器控制

一个基于Node.js的远程浏览器控制服务，支持动态代理配置、自动超时管理和页面元素可配置，特别适合处理步骤固定但页面元素不同的场景（如多页面统一流程操作）。

## 核心功能

- **动态代理支持**：客户端创建浏览器实例时可指定任意代理地址，无需预先配置
- **自动超时管理**：浏览器实例5分钟无操作自动关闭，也支持自定义超时时间
- **页面元素可配置**：通过API动态添加页面元素配置，新增页面无需修改代码
- **标准化步骤执行**：支持固定流程（如输入手机号→获取验证码→提交）的自动化执行

## 技术栈

- 核心框架：Node.js + Express
- 浏览器自动化：Puppeteer（控制无头Chrome/Chromium）
- 接口通信：HTTP RESTful API

## 安装与启动

### 环境要求

- Node.js 18+
- npm 10+

### 安装步骤

1. 克隆项目代码git clone <项目仓库地址>
```shell
cd remote-browser-control
```
2. 安装依赖
```shell
npm install
```
3. 安装puppeteer
```shell
npm install puppeteer@latest --save
```
1. 启动服务(生产环境) 
```shell
npm start
```

### 打包方式

#### pkg打包可执行文件

```shell
npm install -g pkg
pkg . #打包后可执行应用在dist文件夹下打包后可执行应用在dist文件夹下
```

#### 直接打包源码（服务器已安装 Node.js）

##### 项目下

```shell
# 清除node_modules，只安装生产依赖
rm -rf node_modules
npm install --production  # 仅安装dependencies，不安装devDependencies
zip -r browser-control.zip . -x "*.git*" "*.log" "data/*"  # 排除git、日志、数据目录
```

##### 服务器

```shell
unzip browser-control.zip
cd browser-control
# 使用PM2管理进程（推荐，确保服务后台运行）
npm install -g pm2
pm2 start server.js --name "browser-control"
pm2 startup  # 设置开机自启
```

#### docker打包

##### 创建dockerfile

```dockerfile
# 使用Node.js 18基础镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制package.json和依赖锁文件
COPY package*.json ./

# 安装生产依赖
RUN npm install --production

# 复制项目源码
COPY . .

# 创建数据目录（用于持久化页面配置）
RUN mkdir -p /app/data

# 暴露端口（与server.js中一致）
EXPOSE 3000

# 启动命令
CMD ["node", "server.js"]
```

##### 构建 Docker 镜像

```shell
docker build -t browser-control:v1 .  # 镜像名为browser-control，版本v1
docker run -p 3000:3000 -v ./data:/app/data --name browser-server browser-control:v1 # 运行容器，映射端口3000，挂载数据目录（持久化配置）
```

## 开发环境

```  shell
npm run dev
```

服务默认运行在3000端口，可通过环境变量`PORT`修改：PORT=8080 npm start

## 代码结构说明

``` text
├── server.js           # 主服务器，提供HTTP API接口
├── browserManager.js   # 浏览器实例管理，处理创建、关闭和超时
├── stepExecutor.js     # 执行固定步骤（输入手机号、验证码等操作）
├── pageConfigStore.js  # 存储和管理页面元素配置
└── package.json        # 项目依赖配置
```

### 核心模块功能

1. **server.js**
   - 提供RESTful API接口
   - 协调各模块工作
   - 处理客户端请求和响应

2. **browserManager.js**
   - 管理浏览器实例生命周期
   - 实现动态代理配置
   - 处理超时自动关闭逻辑

3. **stepExecutor.js**
   - 实现标准化操作步骤
   - 根据页面配置执行对应元素操作
   - 处理操作过程中的错误和重试

4. **pageConfigStore.js**
   - 存储不同页面的元素配置
   - 支持通过URL自动识别页面类型
   - 提供配置的增删查改功能

## 使用流程示例

以下是完整的使用流程，以两个不同页面（PageA和PageB）的下单操作为例：

### 1. 添加页面配置

#### 添加PageA的配置

```shell
curl -X POST http://localhost:3000/api/page-config/add \
  -H "Content-Type: application/json" \
  -d '{
    "pageKey": "pageA",
    "urlPattern": "https://example.com/pageA",
    "elements": {
      "inputPhone": { "selector": "#phone", "type": "css" },
      "getCodeButton": { "selector": "#get-code", "type": "css" },
      "codeInput": { "selector": "#verify-code", "type": "css" },
      "orderButton": { "selector": "#submit-order", "type": "css" },
      "agreementRadio": { "selector": "#submit-order", "type": "css" },
      "confirmationButton": { "selector": "#submit-order", "type": "css" }
    }
  }'
```

#### 添加PageB的配置（元素选择器不同）

```shell
curl -X POST http://localhost:3000/api/page-config/add \
  -H "Content-Type: application/json" \
  -d '{
    "pageKey": "pageB",
    "urlPattern": "https://example.com/pageB",
    "elements": {
      "inputPhone": { "selector": "//input[@name='mobile']", "type": "xpath" },
      "getCodeButton": { "selector": "//button[text()='获取验证码']", "type": "xpath" },
      "codeInput": { "selector": "//div[contains(@class, 'code-input')]/input", "type": "xpath" },
      "orderButton": { "selector": "//button[@id='place-order']", "type": "xpath" },
      "agreementRadio": { "selector": "//button[@id='place-order']", "type": "xpath" },
      "confirmationButton": { "selector": "//button[@id='place-order']", "type": "xpath" }
    }
  }'
```

### 2. 创建浏览器实例（带代理）

#### 创建带普通代理的实例

```shell
curl -X POST http://localhost:3000/api/browser/create \
  -H "Content-Type: application/json" \
  -d '{"proxy": "http://***************:8080"}'
```

#### 响应示例

```shell
{
   "success": true,
   "browserId": "a1b2c3d4-5678-90ef-ghij-klmnopqrstuv",
   "message": "Browser instance created with proxy: http://***************:8080"
 } 
 ```

### 3. 执行下单步骤

#### 对PageA执行操作

``` shell
curl -X POST http://localhost:3000/api/executor/run-order-steps \
  -H "Content-Type: application/json" \
  -d '{
    "browserId": "a1b2c3d4-5678-90ef-ghij-klmnopqrstuv", // 浏览器id(steps1, steps2)
    "pageKey": "pageA", // 页面id(steps1, steps2)
    "phoneNumber": "13800138000", // 输入手机号(steps1)
    "verifyCode": "654321", // 验证码(steps2)
    "targetUrl": "https://example.com/pageA", // 目标地址(steps1)
    "steps": 1 // 步骤 1.页面打开, (勾选协议), 输入手机号, 点击获取验证码 2.填写验证码, 点击下单, (二次确认)
  }'
```

### 4. 查看活跃浏览器数量

```shell
curl http://localhost:3000/api/browser/count
```

### 5. 手动关闭浏览器实例（可选）

```shell
curl -X POST http://localhost:3000/api/browser/close \
  -H "Content-Type: application/json" \
  -d '{"browserId": "a1b2c3d4-5678-90ef-ghij-klmnopqrstuv"}'
```

## API文档

### 1. 创建浏览器实例

- **URL**: `/api/browser/create`
- **方法**: `POST`
- **请求体**:

```json
  {
    "proxy": "*********************:port",  // 可选，代理地址
    "timeout": 300000                       // 可选，超时时间（毫秒），默认300000ms(5分钟)
  }
```

- **响应**:

  ```json
  {
    "success": true,
    "browserId": "实例ID",
    "message": "创建成功信息"
  }
  ```

### 2. 添加页面配置

- **URL**: `/api/page-config/add`
- **方法**: `POST`
- **请求体**:

```json
  {
    "pageKey": "页面唯一标识",
    "urlPattern": "URL匹配正则（可选）",
    "elements": {
      "元素名称": {
        "selector": "选择器字符串",
        "type": "css|xpath"
      }
    }
  }
  ```

- **响应**:

  ```json
  {
    "success": true,
    "message": "配置添加成功信息"
  }
  ```

### 3. 执行下单步骤

- **URL**: `/api/executor/run-order-steps`
- **方法**: `POST`
- **请求体**:

```json
  {
    "browserId": "浏览器实例ID",
    "pageKey": "页面标识",
    "phoneNumber": "手机号",
    "verifyCode": "验证码",
    "targetUrl": "目标页面URL",
    "steps": "1.发送验证码 2.下单"
  }
  ```

- **响应**:

```json
  {
    "success": true,
    "steps": [步骤执行详情],
    "message": "执行结果信息"
  }
  ```

### 4. 关闭浏览器实例

- **URL**: `/api/browser/close`
- **方法**: `POST`
- **请求体**:

```json
  {
    "browserId": "浏览器实例ID"
  }
```

- **响应**:
  
```json
  {
    "success": true,
    "message": "关闭成功信息"
  }
```

### 5. 获取活跃浏览器数量

- **URL**: `/api/browser/count`
- **方法**: `GET`
- **响应**:

```json
  {
    "success": true,
    "activeCount": 活跃实例数量
  }
```

## 扩展建议

1. **添加认证机制**：为API添加密钥认证，提高安全性
2. **实现操作日志**：记录所有操作和结果，便于审计和调试
3. **开发可视化界面**：提供Web界面管理页面配置和监控浏览器实例
4. **支持更多操作步骤**：扩展步骤执行器，支持更多类型的页面操作
5. **添加错误重试策略**：对易失败的操作（如网络请求）添加智能重试

## 注意事项

- 确保服务器已安装Chrome/Chromium浏览器，Puppeteer可能需要依赖系统浏览器
- 高并发场景下需控制浏览器实例数量，避免资源耗尽
- 代理服务器需确保可用，否则可能导致浏览器启动失败
- 长时间运行可能产生缓存文件，可定期清理Puppeteer的临时目录
